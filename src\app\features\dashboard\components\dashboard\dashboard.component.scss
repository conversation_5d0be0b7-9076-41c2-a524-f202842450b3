.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h1 {
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.welcome-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.user-avatar {
  font-size: 4rem;
  opacity: 0.8;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 20px;
  color: #666;
  font-size: 1.1rem;
}

.dashboard-content {
  .stats-section {
    margin-bottom: 30px;
  }

  .main-content {
    margin-bottom: 30px;
  }

  .role-sections {
    .manager-section,
    .hr-section,
    .admin-section {
      margin-bottom: 20px;
    }

    .section-card {
      background: white;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .section-header {
      background: #f8f9fa;
      padding: 20px;
      border-bottom: 1px solid #e9ecef;

      h3 {
        margin: 0;
        color: #333;
        font-size: 1.3rem;

        i {
          margin-right: 10px;
          color: #667eea;
        }
      }
    }

    .section-content {
      padding: 20px;

      p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
      }

      .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;

        .btn {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 10px 20px;
          text-decoration: none;
          border-radius: 5px;
          font-weight: 500;
          transition: all 0.3s ease;

          i {
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }

  .welcome-section {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .welcome-content h1 {
      font-size: 1.5rem;
    }

    .user-avatar {
      font-size: 3rem;
      margin-top: 15px;
    }
  }

  .role-sections .section-content .action-buttons {
    flex-direction: column;

    .btn {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .welcome-section {
    padding: 15px;

    .welcome-content h1 {
      font-size: 1.3rem;
    }

    .welcome-content p {
      font-size: 1rem;
    }
  }
}
